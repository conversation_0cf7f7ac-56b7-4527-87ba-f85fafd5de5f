#ifndef RIGHTDBPARAM_H
#define RIGHTDBPARAM_H

#include "pub_std.h"

//=====================================================================================
//模型名称的参数
//=====================================================================================
struct tagRightParam {
	CString<64>		m_Name;          // 主键
    Jint32			m_id;
};

//=====================================================================================
//模块的参数
//=====================================================================================
struct tagModuleParam {
    CString<64>		modelname;   // 主键
	CString<64>		m_Name;      // 主键
    Jint32			m_id;
    CString<64>		type;   
    CString<64>		unit;
    CString<64>		deadzone;
    CString<64>		ratio;
    CString<64>		isReport;
    CString<64>		userdefine;

};

//=====================================================================================
//DEV_NAME的参数
//=====================================================================================
struct tagDevNameParam {
    CString<64>		model;          // 主键
    CString<64>		port;           // 主键
    CString<64>		addr;           // 主键
    CString<64>		desc;           // 主键
    Jint32			m_id;
    CString<64>		manuID;
    CString<64>		manuName;
    CString<64>		ProType;
    CString<64>		deviceType;
    CString<64>		isReport;
    CString<64>		nodeID;
    CString<64>		productID;
    CString<64>		guid;
    CString<64>		dev;
};

//=====================================================================================
//用户的参数
//=====================================================================================
struct tagUserParam {
	CString<64>		dev;   //主键
    CString<64>		name;   //主键
    Jint32			m_id;
    CString<64>		val;
    CString<64>		unit;
    CString<64>		datatype;
};

struct tagRightMapParam {
    CString<64>			model;		//
    CString<64>			dev;		        //      主键
    CString<64>			event;		        //      主键
    CString<64>			timestamp;		    //      主键  数据库转换为 %.4d-%.2d-%.2d %.2d:%.2d:%.2d.%.3d
    Jint32              m_id;           //索引ID
    CString<64>			timestartgather;		//
    CString<64>			timeendgather;		//
    CString<64>			starttimestamp;		//
    CString<64>			endtimestamp;		//
    CString<64>			HappenSrc;		//
    CString<64>			IsNeedRpt;		//
    CString<64>			occurnum;		//
    CString<64>			EventLevel;     //
    CString<64>			Net_1;		//
};

struct SoeContentParam {
    CString<64>			dev;               //      主键
    CString<64>			event;               //      主键
    CString<64>			etimestamp;               //      主键
    CString<64>			name;               //      主键
    Jint32              m_id;               //索引ID
    CString<64>			val;               
    CString<64>			desc;
    CString<64>			timestamp;
};


//=====================================================================================
//DEV_DATA
//=====================================================================================
struct tagRealParam {
	CString<64>		dev;   //主键
    CString<64>		name;   //主键
    CString<64>		model;   //主键
    Jint32			m_id;
    Jint32			secret; //加密
    CString<64>		val;
    CString<64>		quality;
    CString<64>		num;
    CString<64>		timestamp;
};

//=====================================================================================
//ASN_DATA_0015  记录型数据
//=====================================================================================
struct tagAsnData0015Param      //主表
{
    Jint32			id;
    Jint32			devNo;      //逻辑设备号
    Jint32			starttime;  //采集开始时间
    Jint32			endtime;    //采集结束时间
    Jint32			timer;      //采集存储时标
    Jint32			infoID;     //信息点号
    Jint32			classID;    //数据类标识
    Jint32			mainD_ID;   //主数据ID
    CString<64>		checkcode;  //附加校验
};

struct tagAsnData0015DataParam  //数据表，m_id 与主表id对应 
{
    Jint32			id;         //主键-
    Jint32			m_id;       //m_id 与主表id对应     
    Jint32          addr;       //数据项标识
    Jint32          property;   //属性标识
    CString<256>	str_value;  //数据缓冲
};

//=====================================================================================
//ASN_DATA_0011     //普通实时数据
//=====================================================================================
struct tagAsnData0011Param      //主表
{
    Jint32			id;         //主键
    Jint32			devNo;      //主键-逻辑设备号
    Jint32			infoID;     //主键-信息点号
    Jint32          addr;       //主键-数据项标识
    Jint32          property;   //属性标识
    Jint32			classID;    //数据类标识
    Jint32		    value;      //数据缓冲
};


#endif //RIGHTDBPARAM_H
