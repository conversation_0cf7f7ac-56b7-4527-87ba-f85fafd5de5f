#include "transient.h"
#include <iostream>
#include <cstring>
#include <chrono>
#include <cmath>

// 通道名称：三相电压 + 三相电流
const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};

//=============================================================================
// CircularBuffer 类实现
//=============================================================================

CircularBuffer::CircularBuffer(size_t capacity)
    : m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
{
    if (capacity == 0) {
        throw std::invalid_argument("CircularBuffer capacity cannot be zero");
    }
    m_buffer.resize(capacity);
    std::cout << "CircularBuffer created with capacity: " << capacity << std::endl;
}

CircularBuffer::~CircularBuffer()
{
    Clear();
    std::cout << "CircularBuffer destroyed" << std::endl;
}

void CircularBuffer::Push(const WaveformSample& sample)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_buffer[m_head] = sample;
    m_head = (m_head + 1) % m_capacity;
    
    if (m_size < m_capacity) {
        ++m_size;
    } else {
        // 缓冲区已满，移动尾指针
        m_tail = (m_tail + 1) % m_capacity;
    }
}

size_t CircularBuffer::GetLatestSamples(std::vector<WaveformSample>& samples, size_t count) const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_size == 0 || count == 0) {
        samples.clear();
        return 0;
    }
    
    size_t actualCount = std::min(count, m_size);
    samples.resize(actualCount);
    
    // 从最新的数据开始向前取
    size_t pos = (m_head + m_capacity - 1) % m_capacity;  // 最新数据位置
    
    for (size_t i = 0; i < actualCount; ++i) {
        samples[actualCount - 1 - i] = m_buffer[pos];
        pos = (pos + m_capacity - 1) % m_capacity;
    }
    
    return actualCount;
}

size_t CircularBuffer::Size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size;
}

size_t CircularBuffer::Capacity() const
{
    return m_capacity;
}

bool CircularBuffer::IsEmpty() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == 0;
}

bool CircularBuffer::IsFull() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == m_capacity;
}

void CircularBuffer::Clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_head = 0;
    m_tail = 0;
    m_size = 0;
}

//=============================================================================
// DefaultMessageParser 类实现
//=============================================================================

bool DefaultMessageParser::ParseMessage(const uint8_t* rawData, size_t dataSize, std::vector<WaveformSample>& samples)
{
    if (!rawData || dataSize == 0) {
        std::cout << "Error: Invalid input data" << std::endl;
        return false;
    }
    
    samples.clear();
    
    // 假设报文格式：
    // 报文头(8字节) + 采样点数(4字节) + 采样点数据(每个采样点32字节：时间戳8字节+6个通道*4字节)
    const size_t HEADER_SIZE = 8;
    const size_t SAMPLE_COUNT_SIZE = 4;
    const size_t SAMPLE_SIZE = 32;  // 8字节时间戳 + 6*4字节通道数据
    
    if (dataSize < HEADER_SIZE + SAMPLE_COUNT_SIZE) {
        std::cout << "Error: Message too short: " << dataSize << " bytes" << std::endl;
        return false;
    }
    
    // 跳过报文头
    const uint8_t* dataPtr = rawData + HEADER_SIZE;
    size_t remainingSize = dataSize - HEADER_SIZE;
    
    // 读取采样点数量
    uint32_t sampleCount = 0;
    if (remainingSize >= SAMPLE_COUNT_SIZE) {
        memcpy(&sampleCount, dataPtr, SAMPLE_COUNT_SIZE);
        dataPtr += SAMPLE_COUNT_SIZE;
        remainingSize -= SAMPLE_COUNT_SIZE;
    }
    
    // 验证数据大小
    if (remainingSize < sampleCount * SAMPLE_SIZE) {
        std::cout << "Error: Invalid sample count: " << sampleCount 
                  << ", remaining size: " << remainingSize << std::endl;
        return false;
    }
    
    // 解析采样点数据
    samples.reserve(sampleCount);
    for (uint32_t i = 0; i < sampleCount; ++i) {
        WaveformSample sample;
        if (ParseSingleSample(dataPtr, sample)) {
            samples.push_back(sample);
        }
        dataPtr += SAMPLE_SIZE;
    }
    
    std::cout << "Parsed " << samples.size() << " samples from " << dataSize << " bytes message" << std::endl;
    return !samples.empty();
}

bool DefaultMessageParser::ParseSingleSample(const uint8_t* data, WaveformSample& sample)
{
    if (!data) {
        return false;
    }

    // 解析时间戳（8字节）
    memcpy(&sample.timestamp, data, sizeof(int64_t));
    data += sizeof(int64_t);

    // 解析6个通道数据（每个4字节float）
    for (int i = 0; i < 6; ++i) {
        memcpy(&sample.channels[i], data, sizeof(float));
        data += sizeof(float);
    }

    return true;
}

//=============================================================================
// TransientAnalyzer 类实现
//=============================================================================

TransientAnalyzer::TransientAnalyzer()
    : m_bInitialized(false)
    , m_bRunning(false)
{
}

TransientAnalyzer::~TransientAnalyzer()
{
    Exit();
}

bool TransientAnalyzer::Init()
{
    if (m_bInitialized) {
        return true;
    }

    try {
        // 初始化配置参数
        InitConfig();

        // 创建环形缓冲区
        m_circularBuffer = std::make_unique<CircularBuffer>(m_config.buffer_capacity);

        // 创建默认报文解析器
        m_messageParser = std::make_unique<DefaultMessageParser>();

        m_bInitialized = true;
        std::cout << "TransientAnalyzer initialized successfully." << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "TransientAnalyzer initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void TransientAnalyzer::Exit()
{
    if (!m_bInitialized) {
        return;
    }

    Stop();

    m_circularBuffer.reset();
    m_messageParser.reset();

    m_bInitialized = false;
    std::cout << "TransientAnalyzer exited." << std::endl;
}

bool TransientAnalyzer::Start()
{
    if (!m_bInitialized) {
        return false;
    }

    if (m_bRunning) {
        return true;
    }

    m_bRunning = true;
    std::cout << "TransientAnalyzer started." << std::endl;
    return true;
}

void TransientAnalyzer::Stop()
{
    if (!m_bRunning) {
        return;
    }

    m_bRunning = false;
    std::cout << "TransientAnalyzer stopped." << std::endl;
}

bool TransientAnalyzer::ProcessRawMessage(const uint8_t* rawData, size_t dataSize)
{
    if (!m_bRunning || !rawData || dataSize == 0) {
        return false;
    }

    if (!m_messageParser) {
        return false;
    }

    try {
        // 解析报文数据
        std::vector<WaveformSample> samples;
        if (!m_messageParser->ParseMessage(rawData, dataSize, samples)) {
            return false;
        }

        // 将解析后的采样点添加到环形缓冲区
        for (const auto& sample : samples) {
            AddSampleToBuffer(sample);
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cout << "Error processing message: " << e.what() << std::endl;
        return false;
    }
}

bool TransientAnalyzer::ProcessData(const std::vector<float>& data, int64_t timestamp)
{
    if (!m_bRunning) {
        return false;
    }

    if (data.size() != TransientConfig::CHANNEL_COUNT) {
        std::cout << "Error: Invalid data size, expected " << TransientConfig::CHANNEL_COUNT 
                  << " channels, got " << data.size() << std::endl;
        return false;
    }

    try {
        // 创建采样点并添加到环形缓冲区
        WaveformSample sample(timestamp, data.data());
        AddSampleToBuffer(sample);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "Error processing data: " << e.what() << std::endl;
        return false;
    }
}

size_t TransientAnalyzer::GetBufferSize() const
{
    return m_circularBuffer ? m_circularBuffer->Size() : 0;
}

void TransientAnalyzer::SetMessageParser(std::unique_ptr<MessageParser> parser)
{
    if (parser) {
        m_messageParser = std::move(parser);
    }
}

void TransientAnalyzer::AddSampleToBuffer(const WaveformSample& sample)
{
    if (m_circularBuffer) {
        m_circularBuffer->Push(sample);
    }
}

void TransientAnalyzer::InitConfig()
{
    // 设置默认配置参数
    m_config.sampling_rate = 12800.0;          // 采样率 Hz
    m_config.buffer_capacity = 10000;          // 环形缓冲区容量（10000个采样点）
    
    std::cout << "Config initialized: sampling_rate=" << m_config.sampling_rate 
              << "Hz, buffer_capacity=" << m_config.buffer_capacity << std::endl;
}
