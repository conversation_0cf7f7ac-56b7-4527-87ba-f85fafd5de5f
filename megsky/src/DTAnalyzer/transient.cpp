/*=====================================================================
 * 文件：transient.cpp
 *
 * 描述：暂态录波数据处理模块实现（包含环形缓冲区）
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include "halWaveform.h"
#include <iomanip>
#include <sstream>
#include <stdexcept>
#include <iostream>
#include <sys/types.h>
#include <cmath>
#include <algorithm>
#include <chrono>
#include <unistd.h>
#include <cstring>

//=============================================================================
// TransientConfig 静态成员定义
//=============================================================================

// 通道名称：三相电压 + 三相电流
const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};

// 通道相位标识
const char* TransientConfig::CHANNEL_PHASES[CHANNEL_COUNT] = {
    "A", "B", "C",              // 电压相位
    "A", "B", "C"               // 电流相位
};

// 通道单位
const char* TransientConfig::CHANNEL_UNITS[CHANNEL_COUNT] = {
    "V", "V", "V",              // 电压单位
    "A", "A", "A"               // 电流单位
};

//=============================================================================
// CircularBuffer 类实现
//=============================================================================

CircularBuffer::CircularBuffer(size_t capacity)
    : m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
{
    if (capacity == 0) {
        throw std::invalid_argument("CircularBuffer capacity cannot be zero");
    }
    m_buffer.resize(capacity);
}

CircularBuffer::~CircularBuffer()
{
    Clear();
}

void CircularBuffer::Push(const WaveformSample& sample)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_buffer[m_head] = sample;
    m_head = (m_head + 1) % m_capacity;

    if (m_size < m_capacity) {
        ++m_size;
    } else {
        // 缓冲区已满，移动尾指针
        m_tail = (m_tail + 1) % m_capacity;
    }
}

size_t CircularBuffer::GetLatestSamples(std::vector<WaveformSample>& samples, size_t count) const
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_size == 0 || count == 0) {
        samples.clear();
        return 0;
    }

    size_t actualCount = std::min(count, m_size);
    samples.resize(actualCount);

    // 从最新的数据开始向前取
    size_t pos = (m_head + m_capacity - 1) % m_capacity;  // 最新数据位置

    for (size_t i = 0; i < actualCount; ++i) {
        samples[actualCount - 1 - i] = m_buffer[pos];
        pos = (pos + m_capacity - 1) % m_capacity;
    }

    return actualCount;
}

size_t CircularBuffer::Size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size;
}

size_t CircularBuffer::Capacity() const
{
    return m_capacity;
}

bool CircularBuffer::IsEmpty() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == 0;
}

bool CircularBuffer::IsFull() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == m_capacity;
}

void CircularBuffer::Clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_head = 0;
    m_tail = 0;
    m_size = 0;
}

//=============================================================================
// DefaultMessageParser 类实现
//=============================================================================

bool DefaultMessageParser::ParseMessage(const uint8_t* rawData,std::vector<WaveformSample>& samples)
{
    if (!rawData) {
        return false;
    }

    samples.clear();


    
}

bool DefaultMessageParser::ParseSingleSample(const uint8_t* data, WaveformSample& sample)
{
    if (!data) {
        return false;
    }

    // 解析时间戳（8字节）
    memcpy(&sample.timestamp, data, sizeof(int64_t));
    data += sizeof(int64_t);

    // 解析6个通道数据（每个4字节float）
    for (int i = 0; i < 6; ++i) {
        memcpy(&sample.channels[i], data, sizeof(float));
        data += sizeof(float);
    }

    return true;
}


TransientAnalyzer::TransientAnalyzer()
    : m_bInitialized(false)
    , m_bRunning(false)
    , m_threadRunning(false)
    , m_waveformDevice(nullptr)
    , m_waveformThreadRunning(false)
{
}

TransientAnalyzer::~TransientAnalyzer()
{
    Exit();
}

bool TransientAnalyzer::Init()
{
    if (m_bInitialized) {
        return true;
    }

    try {
        // 初始化配置参数
        InitConfig();

        // 初始化数据缓冲区
        InitDataBuffer();

        // 初始化算法参数
        InitAlgorithmParams();

        // 初始化波形采集设备
        if (!InitWaveformDevice()) {
            IEC_LOG_RECORD(eErrType, "Failed to initialize waveform device.");
            return false;
        }

        // 创建环形缓冲区
        m_circularBuffer = std::make_unique<CircularBuffer>(m_config.buffer_capacity);

        // 创建默认报文解析器
        m_messageParser = std::make_unique<DefaultMessageParser>();

        m_bInitialized = true;
        return true;
    }
    catch (const std::exception& e) {
        return false;
    }
}

void TransientAnalyzer::Exit()
{
    if (!m_bInitialized) {
        return;
    }

    Stop();

    ClearDataBuffer();
    m_circularBuffer.reset();
    m_messageParser.reset();

    m_bInitialized = false;
}
bool TransientAnalyzer::Start()
{
    if (!m_bInitialized) {
        return false;
    }

    if (m_bRunning) {
        return true;
    }

    // 启动数据处理线程
    StartProcessingThread();

    // 启动波形数据采集线程
    StartWaveformThread();

    m_bRunning = true;
    return true;
}

void TransientAnalyzer::Stop()
{
    if (!m_bRunning) {
        return;
    }

    // 停止波形数据采集线程
    StopWaveformThread();

    // 停止数据处理线程
    StopProcessingThread();

    m_bRunning = false;
}

bool TransientAnalyzer::ProcessRawMessage(const uint8_t* rawData, size_t dataSize)
{
    if (!m_bRunning || !rawData || dataSize == 0) {
        return false;
    }

    if (!m_messageParser) {
        return false;
    }

    try {
        // 解析报文数据
        std::vector<WaveformSample> samples;
        if (!m_messageParser->ParseMessage(rawData, dataSize, samples)) {
            return false;
        }

        // 将解析后的采样点添加到环形缓冲区
        for (const auto& sample : samples) {
            AddSampleToBuffer(sample);

            // 检查是否触发暂态事件
            std::vector<float> channelData(sample.channels, sample.channels + 6);
            if (DetectTransient(channelData, sample.timestamp)) {
                // 触发录波
                TriggerRecording(channelData, sample.timestamp);
            }
        }

        // 记录处理统计信息
        static int processCounter = 0;
        if (++processCounter % 100 == 0) { // 每100次记录一次
        }

        return true;
    }
    catch (const std::exception& e) {
        return false;
    }
}

bool TransientAnalyzer::ProcessData(const std::vector<float>& data, int64_t timestamp)
{
    if (!m_bRunning) {
        return false;
    }

    try {
        // 数据预处理检查
        if (!PreprocessData(data)) {
            return false;
        }

        // 创建采样点并添加到环形缓冲区
        WaveformSample sample(timestamp, data.data());
        AddSampleToBuffer(sample);

        // 将数据包加入队列，由处理线程异步处理
        {
            std::lock_guard<std::mutex> lock(m_queueMutex);

            // 检查队列是否已满
            if (m_dataQueue.size() >= MAX_QUEUE_SIZE) {
                m_dataQueue.pop();  // 丢弃最旧的数据包
            }

            // 添加新数据包到队列
            m_dataQueue.emplace(data, timestamp);
        }

        // 通知处理线程有新数据
        m_queueCondition.notify_one();

        return true;
    }
    catch (const std::exception& e) {
        return false;
    }
}

size_t TransientAnalyzer::GetQueueSize() const
{
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_dataQueue.size();
}

size_t TransientAnalyzer::GetBufferSize() const
{
    return m_circularBuffer ? m_circularBuffer->Size() : 0;
}

void TransientAnalyzer::SetMessageParser(std::unique_ptr<MessageParser> parser)
{
    if (parser) {
        m_messageParser = std::move(parser);
    }
}

void TransientAnalyzer::AddSampleToBuffer(const WaveformSample& sample)
{
    if (m_circularBuffer) {
        m_circularBuffer->Push(sample);
    }
}

std::vector<WaveformSample> TransientAnalyzer::GetTriggerData(int64_t triggerTime)
{
    std::vector<WaveformSample> triggerData;

    if (!m_circularBuffer) {
        return triggerData;
    }

    // 计算触发前后的时间范围
    int64_t preTriggerTime = static_cast<int64_t>(m_config.pre_trigger_samples * 1000000 / m_config.sampling_rate);
    int64_t postTriggerTime = static_cast<int64_t>(m_config.post_trigger_samples * 1000000 / m_config.sampling_rate);

    int64_t startTime = triggerTime - preTriggerTime;
    int64_t endTime = triggerTime + postTriggerTime;

    // 从环形缓冲区获取指定时间范围的数据
    m_circularBuffer->GetSamplesByTimeRange(triggerData, startTime, endTime);


    return triggerData;
}

//=============================================================================
// 私有方法实现
//=============================================================================

void TransientAnalyzer::InitConfig()
{
    // 设置默认配置参数
    m_config.sampling_rate = 12800.0;          // 采样率 Hz
    m_config.voltage_threshold = 380.0 * 1.2;  // 电压触发阈值 (额定电压的1.2倍)
    m_config.current_threshold = 100.0 * 1.5;  // 电流触发阈值 (额定电流的1.5倍)
    m_config.pre_trigger_samples = 1024;       // 触发前采样点数
    m_config.post_trigger_samples = 2048;      // 触发后采样点数
    m_config.buffer_capacity = 10000;          // 环形缓冲区容量（10000个采样点）
}


void TransientAnalyzer::InitAlgorithmParams()
{
    // 初始化算法相关参数
    m_lastTriggerTime = 0;
    m_triggerCooldown = 1000000; // 1秒冷却时间（微秒）
}



void TransientAnalyzer::ClearDataBuffer()
{
    if (m_circularBuffer) {
        m_circularBuffer->Clear();
    }
}


bool TransientAnalyzer::GenerateSimulatedWaveformData(void* data, int32_t dsize, uint32_t lastn)
{
    if (!data || dsize <= 0) {
        return false;
    }

    float* floatData = static_cast<float*>(data);
    int samples_per_cycle = static_cast<int>(m_config.sampling_rate / 50.0); // 50Hz系统
    int total_samples = samples_per_cycle * lastn;
    int total_points = total_samples * TransientConfig::CHANNEL_COUNT;

    if (dsize < total_points * sizeof(float)) {
        return false;
    }

    // 生成模拟的三相电压电流波形数据
    for (int sample = 0; sample < total_samples; ++sample) {
        double time = sample / m_config.sampling_rate; // 时间（秒）

        for (int ch = 0; ch < TransientConfig::CHANNEL_COUNT; ++ch) {
            int index = sample * TransientConfig::CHANNEL_COUNT + ch;

            if (ch < 3) {  // 电压通道 (UA, UB, UC)
                double phase_offset = ch * 2.0 * M_PI / 3.0;  // 120度相位差
                double voltage = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * time + phase_offset);
                floatData[index] = static_cast<float>(voltage);
            } else {       // 电流通道 (IA, IB, IC)
                double phase_offset = (ch - 3) * 2.0 * M_PI / 3.0 - M_PI / 6.0;  // 120度相位差 + 30度滞后
                double current = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * time + phase_offset);
                floatData[index] = static_cast<float>(current);
            }
        }
    }

    return true;
}

std::vector<float> TransientAnalyzer::ConvertWaveformToChannels(const std::vector<float>& waveformData, int samplesPerCycle)
{
    // 从波形数据中提取最新的一个采样点的6通道数据
    // 这里我们取最后一个采样点作为当前瞬时值
    std::vector<float> channelData(TransientConfig::CHANNEL_COUNT);

    if (waveformData.size() >= TransientConfig::CHANNEL_COUNT) {
        // 取最后一个完整的采样点
        int lastSampleIndex = (waveformData.size() / TransientConfig::CHANNEL_COUNT - 1) * TransientConfig::CHANNEL_COUNT;

        for (int ch = 0; ch < TransientConfig::CHANNEL_COUNT; ++ch) {
            channelData[ch] = waveformData[lastSampleIndex + ch];
        }

        // 记录采样数据（仅在调试时启用）
        static int logCounter = 0;
        if (++logCounter % 100 == 0) { // 每100次记录一次，避免日志过多

        }
    } else {
        // 填充零值
        std::fill(channelData.begin(), channelData.end(), 0.0f);
    }

    return channelData;
}

bool TransientAnalyzer::PreprocessData(const std::vector<float>& data)
{
    if (data.size() != TransientConfig::CHANNEL_COUNT) {
        return false;
    }

    // 数据有效性检查
    for (size_t i = 0; i < data.size(); ++i) {
        if (std::isnan(data[i]) || std::isinf(data[i])) {
            return false;
        }
    }

    return true;
}

bool TransientAnalyzer::DetectTransient(const std::vector<float>& data, int64_t timestamp)
{
    // 检查冷却时间
    if (timestamp - m_lastTriggerTime <= m_triggerCooldown) {
        return false;
    }

    // 分别检测电压和电流通道
    for (size_t i = 0; i < data.size(); ++i) {
        float threshold;
        const char* type;

        if (i < 3) {  // 电压通道 (UA, UB, UC)
            threshold = m_config.voltage_threshold;
            type = "voltage";
        } else {      // 电流通道 (IA, IB, IC)
            threshold = m_config.current_threshold;
            type = "current";
        }

        if (std::abs(data[i]) > threshold) {
            return true;
        }
    }

    return false;
}

void TransientAnalyzer::TriggerRecording(const std::vector<float>& data, int64_t timestamp)
{
    m_lastTriggerTime = timestamp;

    // 从环形缓冲区获取触发相关的数据
    std::vector<WaveformSample> triggerData = GetTriggerData(timestamp);

    // if (triggerData.empty()) {
    //     IEC_LOG_RECORD(eWarnType, "No trigger data available in buffer.");
    //     return;
    // }

    // // 创建COMTRADE配置
    // ComtradeConfig config = CreateComtradeConfig(timestamp);

    // // 生成文件名
    // std::string filename = GenerateFilename(timestamp);

    // // 写入配置文件
    // if (ComtradeWriter::write_cfg(filename + ".cfg", config)) {
    //     IEC_LOG_RECORD(eRunType, "COMTRADE config file created: %s.cfg", filename.c_str());
    // } else {
    //     IEC_LOG_RECORD(eErrType, "Failed to create COMTRADE config file: %s.cfg", filename.c_str());
    // }

    // // 准备采样数据（从环形缓冲区数据转换）
    // std::vector<SamplePoint> samples = PrepareRecordingDataFromBuffer(triggerData);

    // // 写入数据文件
    // if (ComtradeWriter::write_dat_ascii(filename + ".dat", config, samples)) {
    //     IEC_LOG_RECORD(eRunType, "COMTRADE data file created: %s.dat with %zu samples",
    //                    filename.c_str(), samples.size());
    // } else {
    //     IEC_LOG_RECORD(eErrType, "Failed to create COMTRADE data file: %s.dat", filename.c_str());
    // }
}

ComtradeConfig TransientAnalyzer::CreateComtradeConfig(int64_t timestamp)
{
    ComtradeConfig config;

    config.station_name = "DTAnalyzer_Station";
    config.recorder_id = "DTAnalyzer_001";
    config.frequency = 50.0;

    // 创建6个模拟量通道：三相电压 + 三相电流
    for (int i = 0; i < TransientConfig::CHANNEL_COUNT; ++i) {
        AnalogChannel channel;
        channel.index = i + 1;
        channel.name = TransientConfig::CHANNEL_NAMES[i];
        channel.phase = TransientConfig::CHANNEL_PHASES[i];
        channel.unit = TransientConfig::CHANNEL_UNITS[i];

        // 根据通道类型设置元素类型和转换因子
        if (i < 3) {  // 电压通道
            channel.element = "V";
            channel.factor_a = 0.1;    // 电压转换因子 (假设ADC满量程对应1000V)
            channel.factor_b = 0.0;
            channel.min_sample = -32768;
            channel.max_sample = 32767;
        } else {      // 电流通道
            channel.element = "I";
            channel.factor_a = 0.01;   // 电流转换因子 (假设ADC满量程对应1000A)
            channel.factor_b = 0.0;
            channel.min_sample = -32768;
            channel.max_sample = 32767;
        }

        channel.offset_time = 0;
        config.analog_channels.push_back(channel);
    }

    // 设置采样率
    SamplingRate rate;
    rate.rate = m_config.sampling_rate;
    rate.points = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    config.sampling_rates.push_back(rate);

    // 设置时间戳
    config.start_time = FormatTimestamp(timestamp - m_config.pre_trigger_samples * 1000000 / m_config.sampling_rate);
    config.end_time = FormatTimestamp(timestamp + m_config.post_trigger_samples * 1000000 / m_config.sampling_rate);

    return config;
}

std::vector<SamplePoint> TransientAnalyzer::PrepareRecordingData(int64_t timestamp)
{
    std::vector<SamplePoint> samples;

    size_t total_samples = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    samples.reserve(total_samples);

    for (size_t i = 0; i < total_samples; ++i) {
        SamplePoint sample;
        sample.index = static_cast<int>(i + 1);
        sample.time = static_cast<int64_t>(i * 1000000 / m_config.sampling_rate);

        // 从缓冲区获取数据（这里简化处理，实际应该从环形缓冲区获取）
        sample.analog_values.resize(TransientConfig::CHANNEL_COUNT);

        for (int ch = 0; ch < TransientConfig::CHANNEL_COUNT; ++ch) {
            if (ch < 3) {  // 电压通道 (UA, UB, UC)
                // 模拟三相电压数据 (相位差120度)
                double phase_offset = ch * 2.0 * M_PI / 3.0;  // 120度相位差
                double voltage = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * i / m_config.sampling_rate + phase_offset);
                sample.analog_values[ch] = static_cast<int>(voltage / 0.1);  // 根据转换因子转换
            } else {       // 电流通道 (IA, IB, IC)
                // 模拟三相电流数据 (相位差120度，滞后电压30度)
                double phase_offset = (ch - 3) * 2.0 * M_PI / 3.0 - M_PI / 6.0;  // 120度相位差 + 30度滞后
                double current = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * i / m_config.sampling_rate + phase_offset);
                sample.analog_values[ch] = static_cast<int>(current / 0.01);  // 根据转换因子转换
            }
        }

        samples.push_back(sample);
    }

    return samples;
}

std::vector<SamplePoint> TransientAnalyzer::PrepareRecordingDataFromBuffer(const std::vector<WaveformSample>& triggerData)
{
    std::vector<SamplePoint> samples;
    samples.reserve(triggerData.size());

    for (size_t i = 0; i < triggerData.size(); ++i) {
        const WaveformSample& waveformSample = triggerData[i];

        SamplePoint sample;
        sample.index = static_cast<int>(i + 1);
        sample.time = waveformSample.timestamp;
        sample.analog_values.resize(TransientConfig::CHANNEL_COUNT);

        // 转换波形数据到COMTRADE格式
        for (int ch = 0; ch < TransientConfig::CHANNEL_COUNT; ++ch) {
            if (ch < 3) {  // 电压通道
                sample.analog_values[ch] = static_cast<int>(waveformSample.channels[ch] / 0.1);  // 电压转换因子
            } else {       // 电流通道
                sample.analog_values[ch] = static_cast<int>(waveformSample.channels[ch] / 0.01); // 电流转换因子
            }
        }

        samples.push_back(sample);
    }
    return samples;
}

std::string TransientAnalyzer::GenerateFilename(int64_t timestamp)
{
    time_t t = timestamp / 1000000;
    struct tm* tm_info = localtime(&t);

    char buffer[256];
    snprintf(buffer, sizeof(buffer), "%s/TRANS_%04d%02d%02d_%02d%02d%02d",
             COMTRADE_PATH,
             tm_info->tm_year + 1900,
             tm_info->tm_mon + 1,
             tm_info->tm_mday,
             tm_info->tm_hour,
             tm_info->tm_min,
             tm_info->tm_sec);

    return std::string(buffer);
}

std::string TransientAnalyzer::FormatTimestamp(int64_t timestamp)
{
    time_t t = timestamp / 1000000;
    int microseconds = timestamp % 1000000;
    struct tm* tm_info = localtime(&t);

    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%02d/%02d/%02d,%02d:%02d:%02d.%06d",
             tm_info->tm_mon + 1,
             tm_info->tm_mday,
             tm_info->tm_year % 100,
             tm_info->tm_hour,
             tm_info->tm_min,
             tm_info->tm_sec,
             microseconds);

    return std::string(buffer);
}

//=============================================================================
// ComtradeWriter 类实现
//=============================================================================

bool ComtradeWriter::write_cfg(const std::string& filename, const ComtradeConfig& config) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        // 写入厂站名称和录波器编号
        file << config.station_name << "," << config.recorder_id << "\n";

        // 写入通道数量信息（仅模拟量通道）
        file << config.total_channels() << ","
             << config.analog_count() << "A,"
             << "0D\n";  // 数字量通道数固定为0

        // 写入模拟量通道信息（三相电压+三相电流）
        for (const auto& analog : config.analog_channels) {
            file << analog.index << ","
                 << analog.name << ","
                 << analog.phase << ","
                 << analog.element << ","
                 << analog.unit << ","
                 << std::fixed << std::setprecision(6) << analog.factor_a << ","
                 << std::fixed << std::setprecision(6) << analog.factor_b << ","
                 << analog.offset_time << ","
                 << analog.min_sample << ","
                 << analog.max_sample << "\n";
        }

        // 写入频率信息
        file << std::fixed << std::setprecision(1) << config.frequency << "\n";

        // 写入采样率数量
        file << config.sampling_rates.size() << "\n";

        // 写入各采样率信息
        for (const auto& rate : config.sampling_rates) {
            file << std::fixed << std::setprecision(1) << rate.rate << ","
                 << rate.points << "\n";
        }

        // 写入开始时间和结束时间
        file << config.start_time << "\n";
        file << config.end_time << "\n";

        // 写入文件类型(固定为ASCII)
        file << "ASCII\n";

        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_dat_ascii(const std::string& filename, const ComtradeConfig& config,
                                    const std::vector<SamplePoint>& samples) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        // 验证采样点数据与配置是否匹配
        for (const auto& sample : samples) {
            if (sample.analog_values.size() != config.analog_count()) {
                return false; // 采样点数据与配置的通道数量不匹配
            }
        }

        // 写入所有采样点
        for (const auto& sample : samples) {
            file << sample.index << "," << sample.time;
            for (int value : sample.analog_values) {
                file << "," << value;
            }
            file << "\n";
        }

        return true;
    }
    catch (...) {
        return false;
    }
}