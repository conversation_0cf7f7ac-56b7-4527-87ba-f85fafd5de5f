# 暂态录波模块 (Transient Recording Module)

## 概述

本模块为 DTAnalyzer 项目提供了完整的暂态录波功能，专门针对三相电压和三相电流（共6个通道）进行暂态事件检测、数据录制和 COMTRADE 格式文件生成。

## 文件结构

```
megsky/src/DTAnalyzer/
├── transient.h                    # 暂态录波模块头文件
├── transient.cpp                  # 暂态录波模块实现
├── transient_usage_example.cpp    # 使用示例
└── TRANSIENT_README.md            # 本文档
```

## 主要类和结构

### 1. TransientAnalyzer 类

主要的暂态分析器类，负责：
- 实时数据处理
- 暂态事件检测
- 录波触发和数据记录
- COMTRADE 文件生成

#### 主要方法：

```cpp
class TransientAnalyzer {
public:
    TransientAnalyzer();                    // 构造函数
    ~TransientAnalyzer();                   // 析构函数
    
    bool Init();                            // 初始化
    void Exit();                            // 退出清理
    bool Start();                           // 启动分析器
    void Stop();                            // 停止分析器
    
    // 核心数据处理方法
    bool ProcessData(const std::vector<float>& data, int64_t timestamp);
    
    // 状态查询
    bool IsRunning() const;
    bool IsInitialized() const;
};
```

### 2. TransientConfig 结构

配置参数结构：

```cpp
struct TransientConfig {
    double sampling_rate;           // 采样率 (Hz)
    double voltage_threshold;       // 电压触发阈值 (V)
    double current_threshold;       // 电流触发阈值 (A)
    int pre_trigger_samples;        // 触发前采样点数
    int post_trigger_samples;       // 触发后采样点数

    static const int CHANNEL_COUNT = 6;  // 固定6通道：三相电压+三相电流
    static const char* CHANNEL_NAMES[CHANNEL_COUNT];    // 通道名称：UA,UB,UC,IA,IB,IC
    static const char* CHANNEL_PHASES[CHANNEL_COUNT];   // 相位标识：A,B,C,A,B,C
    static const char* CHANNEL_UNITS[CHANNEL_COUNT];    // 单位：V,V,V,A,A,A
};
```

### 3. ComtradeWriter 类

COMTRADE 文件写入器（保持原有功能）：

```cpp
class ComtradeWriter {
public:
    static bool write_cfg(const std::string& filename, const ComtradeConfig& config);
    static bool write_dat_ascii(const std::string& filename, const ComtradeConfig& config, 
                               const std::vector<SamplePoint>& samples);
    static bool write_sample_ascii(std::ofstream& file, const SamplePoint& sample);
};
```

## 使用方法

### 基本使用

```cpp
#include "transient.h"

// 1. 创建分析器实例
TransientAnalyzer analyzer;

// 2. 初始化
if (!analyzer.Init()) {
    // 处理初始化失败
    return;
}

// 3. 启动
if (!analyzer.Start()) {
    // 处理启动失败
    return;
}

// 4. 处理实时数据
std::vector<float> data(TransientConfig::CHANNEL_COUNT);  // 6通道数据：三相电压+三相电流
// 填充数据：data[0-2]为UA,UB,UC电压，data[3-5]为IA,IB,IC电流
int64_t timestamp = getCurrentTimestamp();
analyzer.ProcessData(data, timestamp);

// 5. 停止和清理
analyzer.Stop();
```

### 在 CTaskManager 中集成

```cpp
class CTaskManager {
private:
    TransientAnalyzer m_transientAnalyzer;
    
public:
    bool Init() {
        // 初始化暂态分析器
        if (!m_transientAnalyzer.Init()) {
            return false;
        }
        return m_transientAnalyzer.Start();
    }
    
    void ProcessRealtimeData(const std::vector<float>& data, int64_t timestamp) {
        // 暂态分析
        m_transientAnalyzer.ProcessData(data, timestamp);
        
        // 其他处理...
    }
};
```

## 配置参数

默认配置参数：

- **采样率**: 12800 Hz
- **电压触发阈值**: 456V (380V × 1.2)
- **电流触发阈值**: 150A (100A × 1.5)
- **触发前采样点数**: 1024
- **触发后采样点数**: 2048
- **通道数**: 6 (固定：三相电压UA,UB,UC + 三相电流IA,IB,IC)

可以通过修改 `InitConfig()` 方法来调整这些参数。

### 通道定义

| 通道索引 | 通道名称 | 类型 | 相位 | 单位 | 说明 |
|---------|---------|------|------|------|------|
| 0 | UA | 电压 | A | V | A相电压 |
| 1 | UB | 电压 | B | V | B相电压 |
| 2 | UC | 电压 | C | V | C相电压 |
| 3 | IA | 电流 | A | A | A相电流 |
| 4 | IB | 电流 | B | A | B相电流 |
| 5 | IC | 电流 | C | A | C相电流 |

## 输出文件

当检测到暂态事件时，系统会自动生成 COMTRADE 格式的录波文件：

- **配置文件**: `TRANS_YYYYMMDD_HHMMSS.cfg`
- **数据文件**: `TRANS_YYYYMMDD_HHMMSS.dat`

文件保存路径：`/data/app/DTAnalyzer/commFile/COMTRADE/`

## 暂态检测算法

当前实现了基于电压和电流分别检测的幅值检测算法：
- **电压检测**: 监测三相电压通道(UA,UB,UC)，当任一相电压幅值超过电压阈值时触发
- **电流检测**: 监测三相电流通道(IA,IB,IC)，当任一相电流幅值超过电流阈值时触发
- **冷却机制**: 包含冷却时间机制（默认1秒），避免重复触发
- **相位识别**: 能够识别具体是哪一相发生了暂态事件

## 扩展功能

### 自定义检测算法

可以继承 `TransientAnalyzer` 类来实现自定义的检测算法：

```cpp
class CustomTransientAnalyzer : public TransientAnalyzer {
public:
    bool ProcessDataWithCustomAlgorithm(const std::vector<float>& data, int64_t timestamp) {
        // 自定义预处理
        std::vector<float> processedData = CustomPreprocess(data);
        
        // 调用基类处理
        return ProcessData(processedData, timestamp);
    }
    
private:
    std::vector<float> CustomPreprocess(const std::vector<float>& data) {
        // 实现自定义预处理算法
        // 例如：滤波、小波变换、FFT分析等
        return data;
    }
};
```

## 日志记录

模块使用项目标准的日志系统：

```cpp
IEC_LOG_RECORD(eRunType, "正常运行日志");
IEC_LOG_RECORD(eWarnType, "警告信息");
IEC_LOG_RECORD(eErrType, "错误信息");
```

## 编译说明

确保在 Makefile 中包含新的源文件：

```makefile
SOURCES += transient.cpp
```

## 注意事项

1. **内存管理**: 数据缓冲区会根据配置参数自动调整大小
2. **线程安全**: 当前实现不是线程安全的，如需多线程使用请添加适当的同步机制
3. **文件权限**: 确保输出目录有写入权限
4. **磁盘空间**: 录波文件可能较大，注意磁盘空间管理

## 未来改进

1. 添加更复杂的暂态检测算法（小波变换、频域分析等）
2. 支持二进制格式的 COMTRADE 文件
3. 添加数据压缩功能
4. 实现环形缓冲区优化内存使用
5. 添加网络传输功能
6. 支持多种触发条件组合


