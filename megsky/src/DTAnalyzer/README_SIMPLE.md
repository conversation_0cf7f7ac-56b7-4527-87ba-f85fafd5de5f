# 简化版暂态分析器

## 概述

这是一个简化版的暂态分析器实现，专注于环形缓冲区和波形数据读取功能。根据用户要求，只保留了核心功能，其他功能已被注释掉。

## 主要功能

### 1. 环形缓冲区 (CircularBuffer)
- **功能**：存储波形采样数据，支持线程安全操作
- **特性**：
  - 固定容量，满时自动覆盖最旧数据
  - 线程安全（使用互斥锁）
  - 支持按数量获取最新数据
  - 支持按时间范围获取数据

### 2. 波形数据接收
- **数据格式**：三相电压电流瞬时值
  - UA, UB, UC：三相电压
  - IA, IB, IC：三相电流
- **采样频率**：12.8kHz（每78.125微秒一个采样点）
- **数据处理**：每10ms读取一次波形数据

### 3. 报文解析 (MessageParser)
- **功能**：解析传输过来的报文数据
- **报文格式**：
  ```
  报文头(8字节) + 采样点数(4字节) + 采样点数据
  每个采样点：时间戳(8字节) + 6个通道数据(6*4字节)
  ```
- **解析流程**：
  1. 验证报文完整性
  2. 提取采样点数量
  3. 逐个解析采样点数据
  4. 存入环形缓冲区

## 文件结构

```
megsky/src/DTAnalyzer/
├── transient.h          # 头文件定义
├── transient.cpp        # 简化版实现
├── test_simple.cpp      # 测试程序
├── Makefile.simple      # 编译文件
└── README_SIMPLE.md     # 说明文档
```

## 核心类说明

### WaveformSample
波形数据采样点结构：
```cpp
struct WaveformSample {
    int64_t timestamp;      // 时间戳（微秒）
    float channels[6];      // 6个通道数据：UA,UB,UC,IA,IB,IC
};
```

### CircularBuffer
环形缓冲区类：
```cpp
class CircularBuffer {
public:
    void Push(const WaveformSample& sample);  // 存储波形数据
    size_t GetLatestSamples(std::vector<WaveformSample>& samples, size_t count) const;
    size_t GetSamplesByTimeRange(std::vector<WaveformSample>& samples, 
                                int64_t startTime, int64_t endTime) const;
};
```

### TransientAnalyzer
简化的暂态分析器：
```cpp
class TransientAnalyzer {
public:
    bool ProcessRawMessage(const uint8_t* rawData, size_t dataSize);  // 处理报文
    bool ProcessData(const std::vector<float>& data, int64_t timestamp);  // 处理数据
};
```

## 编译和运行

### 编译
```bash
make -f Makefile.simple
```

### 运行测试
```bash
make -f Makefile.simple run
```

### 清理
```bash
make -f Makefile.simple clean
```

## 测试程序说明

`test_simple.cpp` 包含三个测试：

1. **测试1：直接处理数据**
   - 生成模拟的三相电压电流数据
   - 直接调用 `ProcessData()` 处理

2. **测试2：报文解析**
   - 生成模拟报文数据
   - 调用 `ProcessRawMessage()` 解析

3. **测试3：环形缓冲区功能**
   - 测试缓冲区的存储和获取功能
   - 验证容量限制和数据覆盖

## 配置参数

```cpp
struct TransientConfig {
    double sampling_rate = 12800.0;    // 采样率 (Hz)
    size_t buffer_capacity = 10000;    // 环形缓冲区容量
};
```

## 使用示例

```cpp
// 创建分析器
TransientAnalyzer analyzer;
analyzer.Init();
analyzer.Start();

// 处理报文数据
uint8_t* rawData = ...;  // 报文数据
size_t dataSize = ...;   // 数据大小
analyzer.ProcessRawMessage(rawData, dataSize);

// 或直接处理采样数据
std::vector<float> data(6);  // 6个通道数据
int64_t timestamp = ...;     // 时间戳
analyzer.ProcessData(data, timestamp);

// 获取缓冲区大小
size_t bufferSize = analyzer.GetBufferSize();
```

## 注意事项

1. **线程安全**：环形缓冲区支持多线程访问
2. **内存管理**：使用智能指针管理资源
3. **错误处理**：所有关键操作都有错误检查
4. **性能优化**：避免不必要的内存拷贝

## 已注释的功能

根据用户要求，以下功能已被注释掉：
- COMTRADE文件生成
- 暂态检测算法
- 多线程处理
- 复杂的配置参数
- 文件I/O操作

只保留了环形缓冲区和波形数据接收的核心功能。
