# 暂态录波环形缓冲区实现说明

## 概述

本实现在原有的暂态录波功能基础上，增加了环形缓冲区和报文解析功能，用于高效存储和管理波形数据。

## 主要特性

### 1. 环形缓冲区 (CircularBuffer)

- **高效存储**：使用环形缓冲区存储波形采样点，避免频繁的内存分配
- **线程安全**：支持多线程并发访问，使用互斥锁保护数据
- **自动覆盖**：当缓冲区满时，自动覆盖最旧的数据
- **灵活查询**：支持按时间范围和数量查询数据

#### 主要方法：

```cpp
// 添加采样点
void Push(const WaveformSample& sample);

// 获取最新N个采样点
size_t GetLatestSamples(std::vector<WaveformSample>& samples, size_t count);

// 按时间范围获取采样点
size_t GetSamplesByTimeRange(std::vector<WaveformSample>& samples, 
                            int64_t startTime, int64_t endTime);
```

### 2. 报文解析器 (MessageParser)

- **可扩展设计**：使用接口模式，支持不同的报文格式
- **默认实现**：提供DefaultMessageParser作为参考实现
- **错误处理**：完善的数据验证和错误处理机制

#### 报文格式（默认实现）：

```
报文头(8字节) + 采样点数(4字节) + 采样点数据
每个采样点：时间戳(8字节) + 6个通道数据(每个4字节float)
```

### 3. 数据流程

```
原始报文 → 报文解析器 → 波形采样点 → 环形缓冲区 → 暂态检测 → COMTRADE录波
```

## 配置参数

### TransientConfig 新增参数：

- `buffer_capacity`：环形缓冲区容量（默认10000个采样点）

### 默认配置：

```cpp
m_config.sampling_rate = 12800.0;          // 采样率 Hz
m_config.voltage_threshold = 380.0 * 1.2;  // 电压触发阈值
m_config.current_threshold = 100.0 * 1.5;  // 电流触发阈值
m_config.pre_trigger_samples = 1024;       // 触发前采样点数
m_config.post_trigger_samples = 2048;      // 触发后采样点数
m_config.buffer_capacity = 10000;          // 环形缓冲区容量
```

## 使用方法

### 1. 基本使用

```cpp
#include "transient.h"

// 创建分析器实例
TransientAnalyzer analyzer;

// 初始化
if (!analyzer.Init()) {
    // 处理初始化失败
    return;
}

// 启动
if (!analyzer.Start()) {
    // 处理启动失败
    return;
}

// 处理原始报文数据
uint8_t* rawData = ...; // 原始报文数据
size_t dataSize = ...;  // 数据大小
analyzer.ProcessRawMessage(rawData, dataSize);

// 停止和清理
analyzer.Stop();
analyzer.Exit();
```

### 2. 自定义报文解析器

```cpp
class CustomMessageParser : public MessageParser {
public:
    bool ParseMessage(const uint8_t* rawData, size_t dataSize, 
                     std::vector<WaveformSample>& samples) override {
        // 实现自定义解析逻辑
        return true;
    }
};

// 设置自定义解析器
analyzer.SetMessageParser(std::make_unique<CustomMessageParser>());
```

## 编译和测试

### 编译命令：

```bash
# 编译所有测试程序
make -f Makefile.transient all

# 编译环形缓冲区测试程序
make -f Makefile.transient test_circular_buffer

# 编译波形测试程序
make -f Makefile.transient test_waveform
```

### 运行测试：

```bash
# 运行环形缓冲区测试
make -f Makefile.transient run-buffer

# 运行波形测试
make -f Makefile.transient run-waveform

# 运行所有测试
make -f Makefile.transient run-all
```

### 调试：

```bash
# 使用gdb调试环形缓冲区测试
make -f Makefile.transient debug-buffer

# 使用gdb调试波形测试
make -f Makefile.transient debug-waveform
```

## 性能特性

### 内存使用：

- 环形缓冲区：`buffer_capacity * sizeof(WaveformSample)` 字节
- 每个WaveformSample：8字节时间戳 + 6*4字节通道数据 = 32字节
- 默认配置下：10000 * 32 = 320KB

### 处理能力：

- 支持12.8kHz采样率的实时数据处理
- 环形缓冲区可存储约0.78秒的数据（10000点/12800Hz）
- 支持多线程并发处理

## 输出文件

当检测到暂态事件时，系统会从环形缓冲区提取相关数据并生成COMTRADE文件：

- **配置文件**: `TRANS_YYYYMMDD_HHMMSS.cfg`
- **数据文件**: `TRANS_YYYYMMDD_HHMMSS.dat`
- **保存路径**: `data/app/DTAnalyzer/commFile/COMTRADE/`

## 注意事项

1. **内存管理**：环形缓冲区会根据配置自动管理内存
2. **线程安全**：所有公共接口都是线程安全的
3. **数据一致性**：使用互斥锁确保数据一致性
4. **错误处理**：完善的错误检查和日志记录
5. **性能优化**：避免频繁的内存分配和拷贝

## 扩展功能

### 未来可能的改进：

1. **压缩存储**：对历史数据进行压缩存储
2. **多级缓冲**：实现多级缓冲区提高性能
3. **网络传输**：支持网络传输报文数据
4. **数据库存储**：将数据存储到数据库
5. **实时监控**：提供实时监控接口

## 故障排除

### 常见问题：

1. **编译错误**：检查依赖文件是否存在
2. **运行时错误**：检查日志输出获取详细信息
3. **性能问题**：调整缓冲区大小和线程参数
4. **内存泄漏**：使用valgrind等工具检查

### 调试技巧：

1. 启用详细日志输出
2. 使用gdb调试器
3. 监控系统资源使用
4. 检查文件权限和路径
