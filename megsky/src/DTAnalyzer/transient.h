
#ifndef TRANSIENT_H
#define TRANSIENT_H

#include <vector>
#include <cstdint>
#include <mutex>
#include <memory>


/**
 * 波形数据采样点结构
 */
struct WaveformSample {
    int64_t timestamp;                  // 时间戳（微秒）
    float channels[6];                  // 6个通道数据：UA,UB,UC,IA,IB,IC

    WaveformSample() : timestamp(0) {
        for (int i = 0; i < 6; ++i) {
            channels[i] = 0.0f;
        }
    }

    WaveformSample(int64_t ts, const float* data) : timestamp(ts) {
        for (int i = 0; i < 6; ++i) {
            channels[i] = data[i];
        }
    }
};

//环形存储区
class CircularBuffer {
public:
    explicit CircularBuffer(size_t capacity);
    ~CircularBuffer();

    void Push(const WaveformSample& sample);  // 存储波形数据
    size_t GetLatestSamples(std::vector<WaveformSample>& samples, size_t count) const;  // 获取最新n个波形数据

    size_t Size() const;
    size_t Capacity() const;
    bool IsEmpty() const;
    bool IsFull() const;
    void Clear();

private:
    std::vector<WaveformSample> m_buffer;   // 数据缓冲区
    size_t m_capacity;                      // 缓冲区容量
    size_t m_head;                          // 头指针
    size_t m_tail;                          // 尾指针
    size_t m_size;                          // 当前大小
    mutable std::mutex m_mutex;             // 互斥锁
};


class MessageParser {
public:
    virtual ~MessageParser() = default;
    virtual bool ParseMessage(const uint8_t* rawData, size_t dataSize, std::vector<WaveformSample>& samples) = 0;
};


class DefaultMessageParser : public MessageParser {
public:
    bool ParseMessage(const uint8_t* rawData, size_t dataSize, std::vector<WaveformSample>& samples) override;

private:
    bool ParseSingleSample(const uint8_t* data, WaveformSample& sample);
};

struct TransientConfig {
    double sampling_rate;           // 采样率 (Hz)
    size_t buffer_capacity;         // 环形缓冲区容量

    static const int CHANNEL_COUNT = 6;

    static const char* CHANNEL_NAMES[CHANNEL_COUNT];
};

class TransientAnalyzer {
public:
    TransientAnalyzer();
    ~TransientAnalyzer();

    bool Init();
    void Exit();
    bool Start();
    void Stop();

    bool ProcessRawMessage(const uint8_t* rawData, size_t dataSize);

    /**
     * 处理实时数据
     * @param data 采样数据（6个通道：UA,UB,UC,IA,IB,IC）
     * @param timestamp 时间戳（微秒）
     * @return 成功返回true，失败返回false
     */
    bool ProcessData(const std::vector<float>& data, int64_t timestamp);

    bool IsRunning() const { return m_bRunning; }
    bool IsInitialized() const { return m_bInitialized; }
    size_t GetBufferSize() const;
    void SetMessageParser(std::unique_ptr<MessageParser> parser);

private:
    // 初始化方法
    void InitConfig();
    void AddSampleToBuffer(const WaveformSample& sample);

private:
    // 基本状态
    bool m_bInitialized;                    // 初始化标志
    bool m_bRunning;                        // 运行状态标志
    TransientConfig m_config;               // 配置参数

    // 环形缓冲区
    std::unique_ptr<CircularBuffer> m_circularBuffer;  // 环形缓冲区
    std::unique_ptr<MessageParser> m_messageParser;    // 数据解析器
};

#endif