/*=====================================================================
 * 文件：transient.h
 *
 * 描述：暂态录波数据处理模块头文件（包含环形缓冲区）
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#ifndef TRANSIENT_H
#define TRANSIENT_H

#include <fstream>
#include <string>
#include <vector>
#include <cstdint>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>

// 前向声明波形采集设备结构
struct tag_WAVEFORM_SAMPLER_DEVICE;

struct AnalogChannel;
struct SamplingRate;

/**
 * 波形数据采样点结构
 */
struct WaveformSample {
    int64_t timestamp;                  // 时间戳（微秒）
    float channels[6];                  // 6个通道数据：UA,UB,UC,IA,IB,IC

    WaveformSample() : timestamp(0) {
        for (int i = 0; i < 6; ++i) {
            channels[i] = 0.0f;
        }
    }

    WaveformSample(int64_t ts, const float* data) : timestamp(ts) {
        for (int i = 0; i < 6; ++i) {
            channels[i] = data[i];
        }
    }
};

/**
 * 环形缓冲区类
 * 用于存储波形采样数据
 */
class CircularBuffer {
public:

    explicit CircularBuffer(size_t capacity);
    ~CircularBuffer();

    void Push(const WaveformSample& sample);  //存储临时波形数据
    size_t GetLatestSamples(std::vector<WaveformSample>& samples, size_t count) const;  //获取n个波形的数据

    size_t Size() const;
    size_t Capacity() const;
    bool IsEmpty() const;
    bool IsFull() const;
    void Clear();

private:
    std::vector<WaveformSample> m_buffer;   // 数据缓冲区
    size_t m_capacity;                      // 缓冲区容量
    size_t m_head;                          // 头指针
    size_t m_tail;                          // 尾指针
    size_t m_size;                          // 当前大小
    mutable std::mutex m_mutex;             // 互斥锁
};

/**
 * 数据包结构 - 用于线程间数据传递
 */
struct DataPacket {
    std::vector<float> data;        // 三相电压电流数据
    int64_t timestamp;              // 时间戳

    DataPacket() = default;
    DataPacket(const std::vector<float>& d, int64_t t) : data(d), timestamp(t) {}
};

//波形数据解析
class MessageParser {
public:
    virtual ~MessageParser() = default;

    virtual bool ParseMessage(const uint8_t* rawData, size_t dataSize, std::vector<WaveformSample>& samples) = 0;
};

//默认报文解析器实现
class DefaultMessageParser : public MessageParser {
public:
    bool ParseMessage(const uint8_t* rawData, size_t dataSize, std::vector<WaveformSample>& samples) override;

private:
    bool ParseSingleSample(const uint8_t* data, WaveformSample& sample);
};

//comtrade文件格式
class ComtradeConfig {
public:
    std::string station_name;       // 厂站名称
    std::string recorder_id;        // 录波器编号
    std::vector<AnalogChannel> analog_channels;  // 模拟量通道（三相电压+三相电流）
    double frequency;               // 系统频率
    std::vector<SamplingRate> sampling_rates;     // 采样率信息
    std::string start_time;         // 开始时间 (格式: MM/DD/YY,HH:MM:SS.ssssss)
    std::string end_time;           // 结束时间

    // 获取总通道数
    size_t total_channels() const {
        return analog_channels.size();
    }

    // 获取模拟量通道数
    size_t analog_count() const {
        return analog_channels.size();
    }
};

/**
 * 模拟量通道信息
 */
struct AnalogChannel {
    int index;              // 通道索引
    std::string name;       // 通道名称
    std::string phase;      // 相位标识
    std::string element;    // 关联元素
    std::string unit;       // 单位
    double factor_a;        // 转换因子A
    double factor_b;        // 转换因子B
    int offset_time;        // 时间偏移
    int min_sample;         // 最小采样值
    int max_sample;         // 最大采样值
};

/**
 * 采样率信息
 */
struct SamplingRate {
    double rate;            // 采样率 (Hz)
    int points;             // 该采样率下的点数
};

/**
 * 采样点数据（用于COMTRADE输出）
 */
struct SamplePoint {
    int index;                      // 采样点索引
    int64_t time;                   // 时间偏移 (微秒)
    std::vector<int> analog_values; // 模拟量值（三相电压+三相电流，共6个通道）
};

/**
 * 暂态分析器配置结构
 */
struct TransientConfig {
    double sampling_rate;           // 采样率 (Hz)
    double voltage_threshold;       // 电压触发阈值 (V)
    double current_threshold;       // 电流触发阈值 (A)
    int pre_trigger_samples;        // 触发前采样点数
    int post_trigger_samples;       // 触发后采样点数
    size_t buffer_capacity;         // 环形缓冲区容量

    // 固定为6通道（三相电压+三相电流）
    static const int CHANNEL_COUNT = 6;

    // 通道名称定义
    static const char* CHANNEL_NAMES[CHANNEL_COUNT];
    static const char* CHANNEL_PHASES[CHANNEL_COUNT];
    static const char* CHANNEL_UNITS[CHANNEL_COUNT];
};

/**
 * 暂态录波分析器类
 * 负责暂态事件检测和录波文件生成，包含环形缓冲区管理
 */
class TransientAnalyzer {
public:

    TransientAnalyzer();
    ~TransientAnalyzer();

    bool Init();
    void Exit();
    bool Start();
    void Stop();

    /**
     * 处理原始报文数据（线程安全）
     * @param rawData 原始报文数据
     * @param dataSize 数据大小
     * @return 成功返回true，失败返回false
     */
    bool ProcessRawMessage(const uint8_t* rawData, size_t dataSize);

    /**
     * 处理实时数据（线程安全）
     * @param data 采样数据
     * @param timestamp 时间戳（微秒）
     * @return 成功返回true，失败返回false
     */
    bool ProcessData(const std::vector<float>& data, int64_t timestamp);

    bool IsRunning() const { return m_bRunning; }

    bool IsInitialized() const { return m_bInitialized; }

    size_t GetQueueSize() const;

    size_t GetBufferSize() const;
    void SetMessageParser(std::unique_ptr<MessageParser> parser);

private:
    // 初始化方法
    void InitConfig();
    void InitDataBuffer();
    void InitAlgorithmParams();
    void ClearDataBuffer();
    bool InitWaveformDevice();

    // 线程相关方法
    void StartProcessingThread();
    void StopProcessingThread();
    void ProcessingThreadFunc();

    // 波形数据采集相关方法
    void StartWaveformThread();
    void StopWaveformThread();
    void WaveformThreadFunc();
    bool ReadWaveformData(void* data, int32_t dsize, uint32_t lastn);
    bool GenerateSimulatedWaveformData(void* data, int32_t dsize, uint32_t lastn);
    std::vector<float> ConvertWaveformToChannels(const std::vector<float>& waveformData, int samplesPerCycle);

    // 数据处理方法
    bool PreprocessData(const std::vector<float>& data);
    bool DetectTransient(const std::vector<float>& data, int64_t timestamp);
    void TriggerRecording(const std::vector<float>& data, int64_t timestamp);

    // 环形缓冲区相关方法
    void AddSampleToBuffer(const WaveformSample& sample);
    std::vector<WaveformSample> GetTriggerData(int64_t triggerTime);

    // COMTRADE文件生成方法
    ComtradeConfig CreateComtradeConfig(int64_t timestamp);
    std::vector<SamplePoint> PrepareRecordingData(int64_t timestamp);
    std::vector<SamplePoint> PrepareRecordingDataFromBuffer(const std::vector<WaveformSample>& triggerData);
    std::string GenerateFilename(int64_t timestamp);
    std::string FormatTimestamp(int64_t timestamp);

private:
    // 基本状态
    bool m_bInitialized;                    // 初始化标志
    bool m_bRunning;                        // 运行状态标志
    TransientConfig m_config;               // 配置参数
    int64_t m_lastTriggerTime;              // 上次触发时间
    int64_t m_triggerCooldown;              // 触发冷却时间

    // 环形缓冲区
    std::unique_ptr<CircularBuffer> m_circularBuffer;  // 环形缓冲区
    std::unique_ptr<MessageParser> m_messageParser;    // 数据解析器

    // 线程相关
    std::thread m_processingThread;         // 数据处理线程
    std::atomic<bool> m_threadRunning;      // 线程运行标志
    std::queue<DataPacket> m_dataQueue;     // 数据队列
    std::mutex m_queueMutex;                // 队列互斥锁
    std::condition_variable m_queueCondition; // 队列条件变量
    static const size_t MAX_QUEUE_SIZE = 1000; // 最大队列大小

    // 波形数据采集相关
    struct tag_WAVEFORM_SAMPLER_DEVICE* m_waveformDevice; // 波形采集设备指针
    std::thread m_waveformThread;           // 波形数据采集线程
    std::atomic<bool> m_waveformThreadRunning; // 波形采集线程运行标志
};

//生成contrade文件
class ComtradeWriter {
public:
    static bool write_cfg(const std::string& filename, const ComtradeConfig& config);
    static bool write_dat_ascii(const std::string& filename, const ComtradeConfig& config,const std::vector<SamplePoint>& samples);
};

#endif