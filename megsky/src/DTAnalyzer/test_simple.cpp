/*=====================================================================
 * 文件：test_simple.cpp
 *
 * 描述：简化版环形缓冲区和波形数据读取测试程序
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include <iostream>
#include <vector>
#include <cstring>
#include <chrono>
#include <cmath>

// 模拟生成三相电压电流数据
void GenerateThreePhaseData(float* channels, double time) {
    // 三相电压 (UA, UB, UC) - 380V有效值，120度相位差
    channels[0] = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * time);                    // UA
    channels[1] = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * time - 2 * M_PI / 3);     // UB
    channels[2] = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * time - 4 * M_PI / 3);     // UC
    
    // 三相电流 (IA, IB, IC) - 100A有效值，滞后电压30度
    channels[3] = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * time - M_PI / 6);         // IA
    channels[4] = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * time - 2 * M_PI / 3 - M_PI / 6); // IB
    channels[5] = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * time - 4 * M_PI / 3 - M_PI / 6); // IC
}

// 模拟生成报文数据
std::vector<uint8_t> GenerateSimulatedMessage(int sampleCount) {
    const size_t HEADER_SIZE = 8;
    const size_t SAMPLE_COUNT_SIZE = 4;
    const size_t SAMPLE_SIZE = 32;  // 8字节时间戳 + 6*4字节通道数据
    
    size_t totalSize = HEADER_SIZE + SAMPLE_COUNT_SIZE + sampleCount * SAMPLE_SIZE;
    std::vector<uint8_t> message(totalSize);
    
    uint8_t* ptr = message.data();
    
    // 写入报文头（8字节，随意填充）
    memset(ptr, 0xAA, HEADER_SIZE);
    ptr += HEADER_SIZE;
    
    // 写入采样点数量
    uint32_t count = static_cast<uint32_t>(sampleCount);
    memcpy(ptr, &count, SAMPLE_COUNT_SIZE);
    ptr += SAMPLE_COUNT_SIZE;
    
    // 写入采样点数据
    auto now = std::chrono::steady_clock::now();
    auto start_time = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()).count();
    
    for (int i = 0; i < sampleCount; ++i) {
        // 时间戳（每个采样点间隔78.125微秒，对应12.8kHz采样率）
        int64_t timestamp = start_time + i * 78;
        memcpy(ptr, &timestamp, sizeof(int64_t));
        ptr += sizeof(int64_t);
        
        // 生成三相电压电流数据
        float channels[6];
        double time = i / 12800.0;  // 12.8kHz采样率
        GenerateThreePhaseData(channels, time);
        
        // 写入6个通道数据
        for (int ch = 0; ch < 6; ++ch) {
            memcpy(ptr, &channels[ch], sizeof(float));
            ptr += sizeof(float);
        }
    }
    
    return message;
}

// 打印三相数据
void PrintThreePhaseData(const WaveformSample& sample) {
    std::cout << "时间戳: " << sample.timestamp << " 微秒" << std::endl;
    std::cout << "三相电压: UA=" << sample.channels[0] << "V, "
              << "UB=" << sample.channels[1] << "V, "
              << "UC=" << sample.channels[2] << "V" << std::endl;
    std::cout << "三相电流: IA=" << sample.channels[3] << "A, "
              << "IB=" << sample.channels[4] << "A, "
              << "IC=" << sample.channels[5] << "A" << std::endl;
    std::cout << "---" << std::endl;
}

int main() {
    std::cout << "=== 简化版环形缓冲区和波形数据读取测试 ===" << std::endl;
    
    // 创建暂态分析器
    TransientAnalyzer analyzer;
    
    // 初始化
    if (!analyzer.Init()) {
        std::cout << "初始化失败!" << std::endl;
        return -1;
    }
    
    // 启动
    if (!analyzer.Start()) {
        std::cout << "启动失败!" << std::endl;
        return -1;
    }
    
    std::cout << "\n=== 测试1：直接处理数据 ===" << std::endl;
    
    // 测试直接处理数据
    for (int i = 0; i < 5; ++i) {
        std::vector<float> data(6);
        double time = i / 12800.0;
        GenerateThreePhaseData(data.data(), time);
        
        auto now = std::chrono::steady_clock::now();
        int64_t timestamp = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()).count();
        
        if (analyzer.ProcessData(data, timestamp)) {
            std::cout << "处理数据 " << i+1 << " 成功" << std::endl;
        }
    }
    
    std::cout << "缓冲区大小: " << analyzer.GetBufferSize() << std::endl;
    
    std::cout << "\n=== 测试2：报文解析 ===" << std::endl;
    
    // 测试报文解析
    std::vector<uint8_t> message = GenerateSimulatedMessage(10);
    std::cout << "生成报文大小: " << message.size() << " 字节" << std::endl;
    
    if (analyzer.ProcessRawMessage(message.data(), message.size())) {
        std::cout << "报文解析成功!" << std::endl;
    } else {
        std::cout << "报文解析失败!" << std::endl;
    }
    
    std::cout << "缓冲区大小: " << analyzer.GetBufferSize() << std::endl;
    
    std::cout << "\n=== 测试3：环形缓冲区功能 ===" << std::endl;
    
    // 测试环形缓冲区
    CircularBuffer buffer(5);  // 创建容量为5的缓冲区
    
    // 添加一些测试数据
    for (int i = 0; i < 8; ++i) {  // 添加8个数据，超过容量
        WaveformSample sample;
        sample.timestamp = i * 1000;
        for (int ch = 0; ch < 6; ++ch) {
            sample.channels[ch] = i * 10 + ch;
        }
        buffer.Push(sample);
        std::cout << "添加数据 " << i << ", 缓冲区大小: " << buffer.Size() << std::endl;
    }
    
    // 获取最新的3个数据
    std::vector<WaveformSample> samples;
    size_t count = buffer.GetLatestSamples(samples, 3);
    std::cout << "\n获取最新 " << count << " 个数据:" << std::endl;
    for (const auto& sample : samples) {
        PrintThreePhaseData(sample);
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    
    // 停止和退出
    analyzer.Stop();
    analyzer.Exit();
    
    return 0;
}
