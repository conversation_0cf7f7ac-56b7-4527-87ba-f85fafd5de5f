# Makefile for simplified transient analyzer
# 简化版暂态分析器编译文件

# 编译器设置
CXX = gcc
CXXFLAGS = -std=c++11 -Wall -Wextra -O2 -x c++
LDFLAGS = -lstdc++ -lpthread

# 目标文件
TARGET = test_simple
OBJS = transient.o test_simple.o

# 默认目标
all: $(TARGET)

# 编译目标程序
$(TARGET): $(OBJS)
	$(CXX) $(OBJS) -o $(TARGET) $(LDFLAGS)

# 编译源文件
transient.o: transient.cpp transient.h
	$(CXX) $(CXXFLAGS) -c transient.cpp -o transient.o

test_simple.o: test_simple.cpp transient.h
	$(CXX) $(CXXFLAGS) -c test_simple.cpp -o test_simple.o

# 清理
clean:
	rm -f $(OBJS) $(TARGET)

# 运行测试
run: $(TARGET)
	./$(TARGET)

# 显示帮助
help:
	@echo "可用目标:"
	@echo "  all     - 编译所有文件"
	@echo "  clean   - 清理编译文件"
	@echo "  run     - 编译并运行测试程序"
	@echo "  help    - 显示此帮助信息"

.PHONY: all clean run help
