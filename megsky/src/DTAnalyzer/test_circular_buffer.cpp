/*=====================================================================
 * 文件：test_circular_buffer.cpp
 *
 * 描述：环形缓冲区和报文解析功能测试程序
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <signal.h>
#include <random>
#include <cstring>

// 全局运行标志
static volatile bool g_running = true;

// 信号处理函数
void signal_handler(int signum) {
    std::cout << "\nReceived signal " << signum << ", stopping..." << std::endl;
    g_running = false;
}

// 模拟报文数据生成器
class MockMessageGenerator {
public:
    MockMessageGenerator() : m_generator(std::random_device{}()) {}
    
    std::vector<uint8_t> GenerateMessage(size_t sampleCount) {
        std::vector<uint8_t> message;
        
        // 报文头(8字节)
        for (int i = 0; i < 8; ++i) {
            message.push_back(0xAA + i);
        }
        
        // 采样点数量(4字节)
        uint32_t count = static_cast<uint32_t>(sampleCount);
        uint8_t* countPtr = reinterpret_cast<uint8_t*>(&count);
        for (int i = 0; i < 4; ++i) {
            message.push_back(countPtr[i]);
        }
        
        // 采样点数据
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
            
        for (size_t i = 0; i < sampleCount; ++i) {
            // 时间戳(8字节)
            int64_t sampleTime = timestamp + i * 78; // 约12.8kHz采样率
            uint8_t* timePtr = reinterpret_cast<uint8_t*>(&sampleTime);
            for (int j = 0; j < 8; ++j) {
                message.push_back(timePtr[j]);
            }
            
            // 6个通道数据(每个4字节float)
            for (int ch = 0; ch < 6; ++ch) {
                float value = GenerateChannelData(ch, i);
                uint8_t* valuePtr = reinterpret_cast<uint8_t*>(&value);
                for (int j = 0; j < 4; ++j) {
                    message.push_back(valuePtr[j]);
                }
            }
        }
        
        return message;
    }
    
private:
    float GenerateChannelData(int channel, size_t sampleIndex) {
        double time = sampleIndex / 12800.0; // 12.8kHz采样率
        
        if (channel < 3) {  // 电压通道
            double phase = channel * 2.0 * M_PI / 3.0;
            double voltage = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * time + phase);
            // 添加少量随机噪声
            std::uniform_real_distribution<float> noise(-5.0f, 5.0f);
            return static_cast<float>(voltage + noise(m_generator));
        } else {  // 电流通道
            double phase = (channel - 3) * 2.0 * M_PI / 3.0 - M_PI / 6.0;
            double current = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * time + phase);
            // 添加少量随机噪声
            std::uniform_real_distribution<float> noise(-1.0f, 1.0f);
            return static_cast<float>(current + noise(m_generator));
        }
    }
    
    std::mt19937 m_generator;
};

int main(int argc, char* argv[])
{
    std::cout << "=== 环形缓冲区和报文解析功能测试程序 ===" << std::endl;
    std::cout << "功能说明：" << std::endl;
    std::cout << "1. 模拟报文数据生成" << std::endl;
    std::cout << "2. 报文解析和数据提取" << std::endl;
    std::cout << "3. 环形缓冲区数据存储" << std::endl;
    std::cout << "4. 按Ctrl+C退出程序" << std::endl;
    std::cout << "=======================================" << std::endl;

    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    try {
        // 创建暂态分析器实例
        TransientAnalyzer analyzer;

        // 初始化分析器
        std::cout << "正在初始化暂态分析器..." << std::endl;
        if (!analyzer.Init()) {
            std::cerr << "错误：暂态分析器初始化失败！" << std::endl;
            return -1;
        }
        std::cout << "暂态分析器初始化成功。" << std::endl;

        // 启动分析器
        std::cout << "正在启动数据处理..." << std::endl;
        if (!analyzer.Start()) {
            std::cerr << "错误：暂态分析器启动失败！" << std::endl;
            return -1;
        }
        std::cout << "数据处理已启动。" << std::endl;

        // 创建模拟报文生成器
        MockMessageGenerator generator;

        // 显示运行状态
        std::cout << "\n=== 运行状态 ===" << std::endl;
        std::cout << "报文生成频率：每100ms生成一次" << std::endl;
        std::cout << "每个报文包含：10个采样点" << std::endl;
        std::cout << "数据内容：三相电压(UA,UB,UC) + 三相电流(IA,IB,IC)" << std::endl;
        std::cout << "环形缓冲区容量：10000个采样点" << std::endl;
        std::cout << "=================" << std::endl;

        // 主循环 - 模拟报文数据处理
        auto start_time = std::chrono::steady_clock::now();
        int message_counter = 0;
        int status_counter = 0;

        while (g_running) {
            // 生成模拟报文数据
            std::vector<uint8_t> messageData = generator.GenerateMessage(10); // 每次10个采样点
            
            // 处理报文数据
            if (analyzer.ProcessRawMessage(messageData.data(), messageData.size())) {
                ++message_counter;
            } else {
                std::cerr << "警告：报文处理失败！" << std::endl;
            }

            // 每5秒显示一次状态信息
            if (++status_counter >= 50) { // 100ms * 50 = 5秒
                status_counter = 0;
                
                auto current_time = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                    current_time - start_time).count();
                
                std::cout << "\n[运行时间: " << elapsed << "s] ";
                std::cout << "处理报文: " << message_counter << " ";
                std::cout << "队列大小: " << analyzer.GetQueueSize() << " ";
                std::cout << "缓冲区大小: " << analyzer.GetBufferSize() << " ";
                std::cout << "状态: " << (analyzer.IsRunning() ? "运行中" : "已停止") << std::endl;
            }

            // 检查分析器状态
            if (!analyzer.IsRunning()) {
                std::cerr << "警告：暂态分析器已停止运行！" << std::endl;
                break;
            }

            // 短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // 停止分析器
        std::cout << "\n正在停止数据处理..." << std::endl;
        analyzer.Stop();
        std::cout << "数据处理已停止。" << std::endl;

        // 退出分析器
        analyzer.Exit();
        std::cout << "暂态分析器已退出。" << std::endl;

        std::cout << "\n=== 测试统计 ===" << std::endl;
        std::cout << "总处理报文数：" << message_counter << std::endl;
        std::cout << "平均处理速度：" << (message_counter * 10) << " 采样点/总时间" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "程序异常：" << e.what() << std::endl;
        return -1;
    }

    std::cout << "\n=== 测试程序结束 ===" << std::endl;
    return 0;
}
